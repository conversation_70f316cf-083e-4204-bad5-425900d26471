<script setup lang="ts">
import { ref } from 'vue'
import { WGridItem, WButton, WAutocomplete, WModal, WParagraph } from '@alice-health/wonderland-vue'
import SubSpecialtiesList from './SubSpecialtiesList.vue'
import type { StaffFormSpecialty, StaffFormSubSpecialty } from '@staff/models'

const props = defineProps<{
  specialties: StaffFormSpecialty[]
  subSpecialties: StaffFormSubSpecialty[]
  internalSubSpecialties: StaffFormSubSpecialty[]
  showInternalSpecialty?: boolean
  suggestedSpecialties?: string
  suggestedSubSpecialties?: []
  disableSpecialtySelect?: boolean
  disableSubSpecialtiesSelect?: boolean
  fields: {
    specialty: StaffFormSpecialty | null
    internalSpecialty: StaffFormSpecialty | null
    subSpecialties: StaffFormSubSpecialty[]
    internalSubSpecialties: StaffFormSubSpecialty[]
  }
}>()

const emit = defineEmits([
  'change-specialty',
  'change-internal-specialty',
  'update:subSpecialties',
  'update:internalSubSpecialties'
])

const showSubSpecialtiesModal = ref(false)
const showInternalSubSpecialtiesModal = ref(false)

const openSubModal = () => (showSubSpecialtiesModal.value = true)
const openInternalModal = () => (showInternalSubSpecialtiesModal.value = true)

const closeSubModal = () => (showSubSpecialtiesModal.value = false)
const closeInternalModal = () => (showInternalSubSpecialtiesModal.value = false)
</script>

<template>
  <WGridItem :column-span="{ xs: 2, sm: 2, md: 2, lg: 2, xl: 2 }">
    <WAutocomplete
      class="flex-1"
      label="Especialidade"
      placeholder="Selecione a especialidade"
      :items="specialties"
      search-by-key="name"
      v-model="fields.specialty"
      :disabled="disableSpecialtySelect"
      @w-select="$emit('change-specialty', $event.detail.id)"
    />
    <WParagraph class="margin-top-12" v-if="suggestedSpecialties" size="medium">
      Especialidade selecionada no formulário: <strong>{{ suggestedSpecialties }}</strong>
    </WParagraph>
  </WGridItem>

  <WGridItem :column-span="{ xs: 2, sm: 2, md: 2, lg: 2, xl: 2 }">
    <WButton :disabled="!fields.specialty" variant="secondary" @click="openSubModal">
      Ver subespecialidades
      {{ fields.subSpecialties?.length ? `(${fields.subSpecialties.length})` : '' }}
    </WButton>
    <WParagraph class="margin-top-12" v-if="suggestedSubSpecialties?.length" size="medium">
      Subespecialidades selecionadas no formulário:
      <template v-for="(sub, index) in suggestedSubSpecialties" :key="index">
        <strong>{{ sub }}</strong>
        <span v-if="suggestedSubSpecialties && index < suggestedSubSpecialties.length - 1">, </span>
      </template>
    </WParagraph>
  </WGridItem>

  <WGridItem v-if="showInternalSpecialty" :column-span="{ xs: 2, sm: 2, md: 2, lg: 2, xl: 2 }">
    <WAutocomplete
      label="Especialidade interna"
      placeholder="Selecione a especialidade interna"
      :items="specialties"
      search-by-key="name"
      v-model="fields.internalSpecialty"
      @w-select="$emit('change-internal-specialty', $event.detail.id)"
    />
  </WGridItem>

  <WGridItem v-if="showInternalSpecialty" :column-span="{ xs: 2, sm: 2, md: 2, lg: 2, xl: 2 }">
    <WButton :disabled="!fields.internalSpecialty" variant="secondary" @click="openInternalModal">
      Ver subespecialidades interna
    </WButton>
  </WGridItem>

  <WModal
    modal-id="sub-specialties-modal"
    modal-title="Subespecialidades"
    :opened="showSubSpecialtiesModal"
    confirm-label="Salvar"
    cancel-label="Cancelar"
    @w-closed="closeSubModal"
  >
    <SubSpecialtiesList
      :disableSubSpecialtiesSelect="props.disableSubSpecialtiesSelect"
      :current-sub-specialties="fields.subSpecialties"
      :sub-specialties="subSpecialties"
      @update:current-sub-specialties="$emit('update:subSpecialties', $event)"
    />
  </WModal>

  <WModal
    modal-id="internal-sub-specialties-modal"
    modal-title="Subespecialidades interna"
    :opened="showInternalSubSpecialtiesModal"
    confirm-label="Salvar"
    cancel-label="Cancelar"
    @w-closed="closeInternalModal"
  >
    <SubSpecialtiesList
      :current-sub-specialties="fields.internalSubSpecialties"
      :sub-specialties="internalSubSpecialties"
      @update:current-sub-specialties="$emit('update:internalSubSpecialties', $event)"
    />
  </WModal>
</template>

<style scoped lang="scss">
.margin-top-12 {
  margin-top: 12px;
}
</style>

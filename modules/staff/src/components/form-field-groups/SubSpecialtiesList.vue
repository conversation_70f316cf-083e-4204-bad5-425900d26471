<script setup lang="ts">
import { WCheckbox } from '@alice-health/wonderland-vue'
import type { StaffFormSubSpecialty } from '@staff/models'

const props = defineProps<{
  disableSubSpecialtiesSelect?: boolean
  currentSubSpecialties: StaffFormSubSpecialty[]
  subSpecialties: StaffFormSubSpecialty[]
}>()

const emit = defineEmits<{
  (e: 'update:currentSubSpecialties', value: StaffFormSubSpecialty[]): void
}>()

const isSubSpecialtySelected = (id: string) => {
  return props.currentSubSpecialties?.some((subSpecialty) => subSpecialty.id === id)
}

const onChangeSubSpecialty = (id: string) => {
  const exists = props.currentSubSpecialties?.some((subSpecialty) => subSpecialty.id === id)
  let updated: StaffFormSubSpecialty[]
  if (exists) {
    updated = props.currentSubSpecialties.filter((subSpecialty) => subSpecialty.id !== id)
  } else {
    const toAdd = props.subSpecialties.find((subSpecialty) => subSpecialty.id === id)
    updated = toAdd ? [...props.currentSubSpecialties, toAdd] : [...props.currentSubSpecialties]
  }
  emit('update:currentSubSpecialties', updated)
}
</script>

<template>
  <ul class="sub-specialties-list">
    <li
      class="sub-specialty-item"
      v-for="subSpecialty in props.subSpecialties"
      :key="subSpecialty.id"
    >
      <WCheckbox
        :disabled="props.disableSubSpecialtiesSelect"
        class="sub-specialty-item--checkbox"
        :label="subSpecialty.name"
        :value="subSpecialty.id"
        :checked="isSubSpecialtySelected(subSpecialty?.id || '')"
        @w-change="onChangeSubSpecialty(subSpecialty?.id || '')"
      />
    </li>
  </ul>
</template>

<style scoped lang="scss">
.sub-specialties-list {
  list-style: none;
  padding: 0;
  margin: 0;
  border: 1px solid var(--sys-color-stroke-active);
  border-radius: var(--gl-border-radius-sm);
  width: 100%;
  max-width: 672px;
  overflow-y: auto;

  .sub-specialty-item {
    display: flex;
    align-items: center;
    gap: var(--gl-spacing-02);
    padding: var(--gl-spacing-04) var(--gl-spacing-06);
    width: 100%;

    &:not(:first-child) {
      border-top: 1px solid var(--sys-color-stroke-active);
    }

    &--checkbox {
      display: flex;
      align-items: center;
      gap: var(--gl-spacing-02);
      flex: 1;
      width: 672px;
    }
  }
}
</style>

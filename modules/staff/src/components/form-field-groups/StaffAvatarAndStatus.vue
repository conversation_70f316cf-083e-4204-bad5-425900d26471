<script setup lang="ts">
import { WAvatar, WSwitch, WGridItem } from '@alice-health/wonderland-vue'
import { type StaffForm } from '@staff/models'
const props = withDefaults(
  defineProps<{
    disableShowAppSwitch?: boolean
    editableAvatar?: boolean
    disableActiveSwitch?: boolean
    showAppSwitch?: boolean
    fields: {
      profileImageUrl?: string
      active: boolean
      showOnApp?: boolean
    }
  }>(),
  {
    editableAvatar: true
  }
)
const emit = defineEmits(['avatar-change'])
const onChangeAvatar = (event: CustomEvent) => {
  emit('avatar-change', event.detail as File)
}
</script>
<template>
  <WGridItem :column-span="{ xs: 2, sm: 2, md: 2, lg: 2, xl: 2 }">
    <div class="staff--form__field">
      <WAvatar
        :editable="props.editableAvatar"
        :url="props?.fields?.profileImageUrl ?? ''"
        size="xlarge"
        @w-change="onChangeAvatar"
      />
      <div class="staff--form__field__switch">
        <WSwitch
          :disabled="props.disableActiveSwitch"
          v-model="props.fields.active"
          label="Ativo"
          size="small"
        />
        <WSwitch
          :disabled="props.disableShowAppSwitch"
          v-if="props.showAppSwitch"
          v-model="props.fields.showOnApp"
          label="Mostrar no App e Site"
          size="small"
        />
      </div>
    </div>
  </WGridItem>
</template>
<style scoped lang="scss">
.staff--form__field {
  display: flex;
  flex-direction: column;
  gap: var(--gl-spacing-08);
}
.staff--form__field__switch {
  display: flex;
  flex-direction: row;
  gap: var(--gl-spacing-08);
}
</style>

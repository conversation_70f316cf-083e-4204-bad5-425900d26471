import { z } from 'zod'

export interface StaffSignupRequestListItem {
  id: string
  name: string
  status: string
}

export const additionalInformationSchema = z.object({
  tier: z.string().min(1, 'Tier é obrigatório'),
  theoristTier: z.string().min(1, 'Tier teórico é obrigatório'),
  showOnApp: z.boolean(),
  specialtyId: z.string().min(1, 'Especialidade é obrigatória'),
  subSpecialtyIds: z.array(z.string()).min(1, 'Pelo menos uma subespecialidade é obrigatória'),
  imageUrl: z.string().optional()
})

export type AdditionalInformation = z.infer<typeof additionalInformationSchema>

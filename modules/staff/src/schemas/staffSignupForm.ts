import { z } from 'zod'

const addressSchema = z.object({
  street: z.string().min(1, 'Rua é obrigatória'),
  number: z.string().min(1, 'Número é obrigatório'),
  complement: z.string().optional(),
  neighborhood: z.string().min(1, 'Bairro é obrigatório'),
  state: z.string().min(1, 'Estado é obrigatório'),
  city: z.string().min(1, 'Cidade é obrigatória'),
  zipcode: z.string().min(1, 'CEP é obrigatório'),
  country: z.string().optional()
})

const phoneSchema = z.object({
  type: z.string().min(1, 'Tipo de telefone é obrigatório'),
  number: z.string().min(1, 'Número de telefone é obrigatório')
})

const contactSchema = z.object({
  address: addressSchema,
  phones: z.array(phoneSchema).min(1, 'Informe pelo menos um telefone'),
  modality: z.string().min(1, 'Modalidade é obrigatória')
})

export const healthProfessionalSchema = z.object({
  active: z.boolean().optional(),
  showOnApp: z.boolean().optional(),
  firstName: z.string().min(1, 'Nome é obrigatório'),
  lastName: z.string().min(1, 'Sobrenome é obrigatório'),
  email: z.string().email('Email inválido'),
  nationalId: z.string().optional(),
  birthdate: z.string().optional(),
  gender: z.string().min(1, 'Gênero é obrigatório'),
  profileImageUrl: z.string().optional(),
  profileBio: z.string().optional(),
  education: z.string().optional(),
  curiosity: z.string().optional(),
  councilType: z.string().min(1, 'Tipo de conselho é obrigatório'),
  councilNumber: z.string().min(1, 'Número do conselho é obrigatório'),
  councilState: z.string().min(1, 'Estado do conselho é obrigatório'),
  specialty: z.string().optional(),
  subSpecialties: z.array(z.string()).optional(),
  internalSpecialty: z
    .object({
      id: z.string(),
      name: z.string()
    })
    .optional(),
  internalSubSpecialties: z
    .array(
      z.object({
        id: z.string(),
        name: z.string()
      })
    )
    .optional(),
  providerUnits: z
    .array(
      z.object({
        id: z.string(),
        name: z.string()
      })
    )
    .optional(),
  selectedSpecialty: z
    .object({
      id: z.string(),
      name: z.string()
    })
    .optional(),
  selectedSubSpecialties: z
    .array(
      z.object({
        id: z.string(),
        name: z.string()
      })
    )
    .optional(),
  selectedInternalSpecialty: z
    .object({
      id: z.string(),
      name: z.string()
    })
    .optional(),
  contacts: z.array(contactSchema).default([]),
  tier: z.string().optional(),
  theoristTier: z.string().optional()
})

export const providerSchema = z.object({
  name: z.string().min(1, 'Nome da unidade é obrigatório'),
  cnpj: z.string().min(1, 'CNPJ é obrigatório'),
  cnes: z.string().optional(),
  bankCode: z.string().optional(),
  agencyNumber: z.string().optional(),
  accountNumber: z.string().optional(),
  phones: z.array(phoneSchema).default([]),
  address: addressSchema
})

export const staffSignupFormSchema = z.object({
  healthProfessional: healthProfessionalSchema,
  provider: providerSchema
})

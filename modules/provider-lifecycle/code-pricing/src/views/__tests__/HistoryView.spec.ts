import { describe, expect, it, beforeAll, afterAll, afterEach } from 'vitest'
import { setupServer } from 'msw/node'
import { http, HttpResponse } from 'msw'
import { fireEvent } from '@testing-library/vue'

import { customRender, screen, waitFor } from '@code-pricing/test'
import { downloadFile } from '@commons/helpers/download'

import HistoryView from '../HistoryView.vue'

const API_BASE = '/resourceBundleSpecialtyPricingUpdate'

const server = setupServer()

server.events.on('request:start', (req) => {
  console.log('MSW request:', req.request.method, req.request.url)
})

vi.mock('@commons/helpers/download', () => ({
  downloadFile: vi.fn()
}))

beforeAll(() => {
  global.URL.createObjectURL = vi.fn(() => 'blob:mock-url')
  server.listen()
})
afterEach(() => {
  server.resetHandlers()
  vi.clearAllMocks()
})
afterAll(() => server.close())

describe('HistoryView', () => {
  const mockHistoryData = {
    pagination: { totalPages: 1 },
    results: [
      {
        id: '1',
        filename: 'test.csv',
        createdAt: '2024-03-20T10:00:00',
        createdBy: 'John Doe',
        status: {
          value: 'PROCESSED',
          friendlyName: 'Processado',
          color: 'SUCCESS'
        },
        processingDetails: {
          friendlyDescription: 'test details',
          errorItems: 0,
          totalItems: 1
        },
        downloadFileUrl: 'https://example.com/download.csv',
        errorItems: false
      },
      {
        id: '2',
        filename: 'error.csv',
        createdAt: '2024-03-21T10:00:00',
        createdBy: 'Jane Doe',
        status: {
          value: 'PARSING_ERROR',
          friendlyName: 'Falha na leitura',
          color: 'ERROR'
        },
        processingDetails: {
          friendlyDescription: 'erro de parsing',
          errorItems: 2,
          totalItems: 2,
          parsingError: 'Arquivo inválido'
        },
        downloadFileUrl: 'https://example.com/error.csv',
        errorItems: true
      }
    ]
  }

  it('should download failed lines file when clicking download error button', async () => {
    const mockBlob = new Blob(['error,line'], { type: 'text/csv' })
    const mockResponse = { data: mockBlob }

    server.use(
      http.get(new RegExp(`${API_BASE}.*`), () => HttpResponse.json(mockHistoryData)),
      http.get(new RegExp(`${API_BASE}/[\\w-]+/failedLinesFile.*`), () =>
        HttpResponse.json(mockResponse)
      )
    )

    const { container } = customRender(HistoryView)

    const errorCell = await screen.findByText(/error\.csv/i)
    expect(errorCell).toBeTruthy()

    const allWButtons = container.querySelectorAll('w-button')
    const errorWButton = allWButtons[allWButtons.length - 1]
    expect(errorWButton).toBeTruthy()
    fireEvent.click(errorWButton)

    await waitFor(() => {
      expect(screen.getByText('Arquivo inválido')).toBeInTheDocument()
    })
  })

  it('should download file when clicking download button', async () => {
    server.use(http.get(new RegExp(`${API_BASE}.*`), () => HttpResponse.json(mockHistoryData)))

    const { user } = customRender(HistoryView)

    await waitFor(() => {
      expect(screen.getByText('test.csv')).toBeInTheDocument()
    })

    const menuButton = screen.getAllByTestId('menu-button')[0]
    await user.click(menuButton)

    const downloadMenuItem = await screen.findByText(/Baixar CSV enviado/i)
    const clickable =
      downloadMenuItem.closest('li') || downloadMenuItem.closest('w-list-item') || downloadMenuItem
    await user.click(clickable)

    expect(downloadFile).toHaveBeenCalled()
  })
})

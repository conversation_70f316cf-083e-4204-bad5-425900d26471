<script lang="ts" setup>
import type {
  WSkeletonComponentProps,
  WSkeletonLayout,
  WTagComponentProps
} from '@alice-health/wonderland/dist/types/models'

import { ref, watch, computed } from 'vue'
import { downloadFile } from '@commons/helpers/download'

import { useQuery } from '@tanstack/vue-query'
import { usePagination } from '@alice-health/vue-hooks'
import { useRoute, useRouter } from 'vue-router'

import { getFailedLinesFile, getHistory, type History } from '@code-pricing/api/queries/history'

import { convertDateToISO } from '@commons/helpers/date'

import {
  WButton,
  WListControllers,
  WTitle,
  WSkeleton,
  WTable,
  WTableHeader,
  WTableHeaderCell,
  WTableBody,
  WTableRow,
  WTableBodyCell,
  WTag,
  WModal,
  WParagraph,
  WSegmentedButtonGroup,
  WSegmentedButton,
  WTextfield,
  WDropdown
} from '@alice-health/wonderland-vue'
import { useSnackbar } from '@commons/hooks'

/*
 * Custom Types
 */

type Status = History['status']['value']
type WTagColor = WTagComponentProps['color']

/*
 * Constants
 */

const tableSkeletonItem: WSkeletonLayout = [{ width: '100%', height: 200, borderRadius: 16 }]

const tableSkeleton: WSkeletonComponentProps = {
  gap: { row: 24, column: 24 },
  layout: [tableSkeletonItem]
}

const headers = [
  { id: 'fileName', label: 'Arquivo enviado' },
  { id: 'date', label: 'Data' },
  { id: 'responsible', label: 'Responsável' },
  { id: 'status', label: 'Status' },
  { id: 'details', label: 'Detalhes' },
  { id: 'actions', width: '64px' }
]

/*
 * Hooks
 */

const route = useRoute()
const router = useRouter()
const { notify } = useSnackbar()

const { setNext, setPrev, currentPage, totalPages, disableNextButton, disablePrevButton } =
  usePagination({ router, route })

/*
 * Refs
 */

const showFilters = ref(false)

const selectedStatusFilterFirstHalf = ref<Status[]>([])
const selectedStatusFilterSecondHalf = ref<Status[]>([])
const selectedStatusFilterFirstHalfNotYetApplied = ref<Status[]>([])
const selectedStatusFilterSecondHalfNotYetApplied = ref<Status[]>([])

const startDate = ref(undefined)
const endDate = ref(undefined)

const startDateNotYetApplied = ref(undefined)
const endDateNotYetApplied = ref(undefined)

const selectedFiltersCount = ref(0)

const errorDetailsModal = ref(false)
const errorDetails = ref<History | null>(null)

/*
 * Requests & Mutations
 */

const params = computed(() => ({
  page: currentPage.value.toString(),
  pageSize: '5',
  filter: convertToFilterParams(startDate.value, endDate.value, [
    ...selectedStatusFilterFirstHalf.value,
    ...selectedStatusFilterSecondHalf.value
  ])
}))

const { data, isLoading, refetch } = useQuery({
  queryKey: ['history', params.value],
  queryFn: () => getHistory(params.value),
  select: ({ pagination, results }) => {
    totalPages.value = pagination.totalPages

    return results
  }
})

/*
 * Methods
 */

function convertToFilterParams(
  startDate: string | undefined,
  endDate: string | undefined,
  status: Status[]
) {
  const startDateFormatted = startDate ? convertDateToISO(startDate) : null
  const endDateFormatted = endDate ? convertDateToISO(endDate) : null

  const filter = {
    status: status,
    startDate: startDateFormatted,
    endDate: endDateFormatted
  }

  return JSON.stringify(filter)
}

function openFilters() {
  startDateNotYetApplied.value = startDate.value

  selectedStatusFilterFirstHalfNotYetApplied.value = selectedStatusFilterFirstHalf.value
  selectedStatusFilterSecondHalfNotYetApplied.value = selectedStatusFilterSecondHalf.value

  endDateNotYetApplied.value = endDate.value
  showFilters.value = !showFilters.value
}

function closeFilters(e: CustomEvent) {
  if (e.detail === 'confirm') {
    applyFilters()
  } else if (e.detail === 'cancel') {
    clearFilters()
  }

  showFilters.value = false
}

function applyFilters() {
  selectedStatusFilterFirstHalf.value = selectedStatusFilterFirstHalfNotYetApplied.value
  selectedStatusFilterSecondHalf.value = selectedStatusFilterSecondHalfNotYetApplied.value

  startDate.value = startDateNotYetApplied.value
  endDate.value = endDateNotYetApplied.value

  selectedFiltersCount.value =
    selectedStatusFilterFirstHalf.value.length + selectedStatusFilterSecondHalf.value.length

  if (startDate.value !== undefined || endDate.value !== undefined) {
    selectedFiltersCount.value = selectedFiltersCount.value + 1
  }

  router.push({ query: { page: '1' } })

  refetch()
}

function clearFilters() {
  selectedStatusFilterFirstHalf.value = []
  selectedStatusFilterSecondHalf.value = []

  startDate.value = undefined
  endDate.value = undefined

  selectedFiltersCount.value = 0
  router.push({ query: { page: '1' } })

  refetch()
}

async function downloadFailedLinesFile(id: string) {
  try {
    const response = await getFailedLinesFile(id)

    const blob = new Blob([response.data], { type: 'text/csv' })

    const url = window.URL.createObjectURL(blob)

    downloadFile(url)
  } catch (error) {
    notify?.({
      message:
        'Ocorreu um erro ao baixar o arquivo, tente novamente, ou verifique se a ação solicitada é válida',
      icon: 'icAlertCircleOutlined'
    })
  }
}

function handleSelectionStatusFirstHalfFilter(e: CustomEvent) {
  selectedStatusFilterFirstHalfNotYetApplied.value = e.detail
}

function handleSelectionStatusSecondHalfFilter(e: CustomEvent) {
  selectedStatusFilterSecondHalfNotYetApplied.value = e.detail
}

function handleSelectAction(e: CustomEvent, row: History) {
  switch (e.detail.value) {
    case 'download-error': {
      downloadFailedLinesFile(row.id)
      break
    }
    case 'download-sent': {
      downloadFile(row.downloadFileUrl)
      break
    }
  }
}

function handleShowErrorDetails(row: History) {
  errorDetailsModal.value = true
  errorDetails.value = row
}

function closeErrorDetails() {
  errorDetailsModal.value = false
  errorDetails.value = null
}

function getListActions(row: History) {
  const errorButtons =
    row.processingDetails.errorItems > 0
      ? [
          {
            label: 'Baixar CSV dos erros',
            icon: 'icDownload',
            value: 'download-error'
          }
        ]
      : []

  const defaultButtons = [
    {
      label: 'Baixar CSV enviado',
      icon: 'icDownload',
      value: 'download-sent'
    }
  ]

  return [...errorButtons, ...defaultButtons]
}
/*
 * Watchers
 */

watch(currentPage, () => refetch())
</script>

<template>
  <div class="history">
    <div class="history__header">
      <WTitle level="3" variant="heavy" size="small">
        Consulte os envios de preços realizados
      </WTitle>
    </div>

    <div class="history__table">
      <WListControllers
        has-pagination
        margin="none"
        hide-input
        :has-filters="true"
        :total-pages="totalPages"
        :current-page="currentPage"
        :disable-next-button="disableNextButton"
        :disable-previous-button="disablePrevButton"
        @WOpenFilters="openFilters"
        @WPaginationNext="setNext"
        @WPaginationPrevious="setPrev"
        :selected-filters-count="selectedFiltersCount"
      />

      <WSkeleton v-if="isLoading" :layout="tableSkeleton.layout" :gap="tableSkeleton.gap" />

      <WTable v-else>
        <WTableHeader slot="header">
          <WTableHeaderCell v-for="header in headers" :key="header.id" :width="header.width">
            {{ header.label }}
          </WTableHeaderCell>
        </WTableHeader>

        <WTableBody slot="body">
          <WTableRow v-if="!data?.length">
            <WTableBodyCell :colspan="headers.length">
              <div class="history__table-body-empty">
                <WParagraph variant="plain" size="large" mode="secondary">
                  Nenhum registro encontrado
                </WParagraph>
              </div>
            </WTableBodyCell>
          </WTableRow>

          <template v-else>
            <WTableRow v-for="row in data" :key="row.id">
              <WTableBodyCell :width="headers[0].width">{{ row.filename }}</WTableBodyCell>

              <WTableBodyCell :width="headers[1].width">
                {{ row.createdAt }}
              </WTableBodyCell>

              <WTableBodyCell :width="headers[2].width">{{ row.createdBy }}</WTableBodyCell>

              <WTableBodyCell :width="headers[3].width">
                <WTag
                  :label="row.status.friendlyName"
                  :color="(row.status.color.toLocaleLowerCase() as WTagColor)"
                />
              </WTableBodyCell>

              <WTableBodyCell :width="headers[4].width">
                {{ row.processingDetails.friendlyDescription ?? '-' }}
              </WTableBodyCell>

              <WTableBodyCell :width="headers[5].width">
                <WButton
                  v-if="row.status.value === 'PARSING_ERROR'"
                  iconButton
                  variant="secondary"
                  icon="icAlertCircleOutlined"
                  @click="handleShowErrorDetails(row)"
                />

                <WDropdown
                  v-else
                  :items="getListActions(row)"
                  @w-select="(event: CustomEvent) => handleSelectAction(event, row)"
                >
                  <WButton
                    iconButton
                    icon="icMenuKebab"
                    variant="secondary"
                    data-testid="menu-button"
                    :disabled="row.status.value === 'PROCESSING'"
                  />
                </WDropdown>
              </WTableBodyCell>
            </WTableRow>
          </template>
        </WTableBody>
      </WTable>
    </div>
    <WModal
      id="filters-modal"
      title="Filtrar"
      confirmLabel="Aplicar filtros"
      cancelLabel="Limpar seleção"
      :opened="showFilters"
      @WClosed="closeFilters"
    >
      <div class="history__filters">
        <div class="history__filters--section">
          <WParagraph variant="heavy" size="large">Status</WParagraph>

          <div>
            <WSegmentedButtonGroup
              :value="selectedStatusFilterFirstHalfNotYetApplied"
              direction="horizontal"
              :onW-change="handleSelectionStatusFirstHalfFilter"
              :multiple="true"
            >
              <WSegmentedButton
                :selected="selectedStatusFilterFirstHalfNotYetApplied.includes('PROCESSING')"
                :flex-grow="true"
                value="PROCESSING"
              >
                Processando
              </WSegmentedButton>

              <WSegmentedButton
                :selected="selectedStatusFilterFirstHalfNotYetApplied.includes('PARSING_ERROR')"
                :flex-grow="true"
                value="PARSING_ERROR"
              >
                Falha na leitura
              </WSegmentedButton>
            </WSegmentedButtonGroup>

            <WSegmentedButtonGroup
              :value="selectedStatusFilterSecondHalfNotYetApplied"
              direction="horizontal"
              :onW-change="handleSelectionStatusSecondHalfFilter"
              :multiple="true"
            >
              <WSegmentedButton
                :selected="selectedStatusFilterSecondHalfNotYetApplied.includes('PROCESSED')"
                :flex-grow="true"
                value="PROCESSED"
              >
                Processado com sucesso
              </WSegmentedButton>

              <WSegmentedButton
                :selected="
                  selectedStatusFilterSecondHalfNotYetApplied.includes('PROCESSED_WITH_ERRORS')
                "
                :flex-grow="true"
                value="PROCESSED_WITH_ERRORS"
              >
                Possui dados incorretos
              </WSegmentedButton>
            </WSegmentedButtonGroup>
          </div>
        </div>

        <div class="history__filters--section">
          <WParagraph variant="heavy" size="large">Período</WParagraph>

          <div class="history__filters--section__dates">
            <WTextfield
              mask-type="date"
              label="Data inicial"
              placeholder="dd/mm/aaaa"
              leading-icon="icCalendar"
              v-model="startDateNotYetApplied"
            />
            <WTextfield
              mask-type="date"
              label="Data final"
              placeholder="dd/mm/aaaa"
              leading-icon="icCalendar"
              v-model="endDateNotYetApplied"
            />
          </div>
        </div>
      </div>
    </WModal>

    <WModal
      id="error-details-modal"
      title="Falha na leitura"
      icon="icAlertCircleFilled"
      confirmLabel="Fechar"
      :opened="errorDetailsModal"
      @WClosed="closeErrorDetails"
    >
      <div class="history__error-details">
        <WParagraph variant="plain" size="large" mode="secondary">
          {{ errorDetails?.processingDetails?.parsingError }}
        </WParagraph>
      </div>
    </WModal>
  </div>
</template>

<style lang="scss" scoped>
.history {
  display: flex;
  flex-direction: column;
  gap: var(--gl-spacing-10);

  &__filters {
    display: flex;
    flex-direction: column;
    gap: var(--gl-spacing-10);
    width: 540px;

    &--section {
      display: flex;
      flex-direction: column;
      gap: var(--gl-spacing-04);

      &__dates {
        display: flex;
        flex-direction: row;
        gap: var(--gl-spacing-04);
        width: 100%;

        & > * {
          flex: 1;
        }
      }
    }
  }

  &__header {
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: var(--gl-spacing-06);
  }

  &__table {
    display: flex;
    flex-direction: column;
    gap: var(--gl-spacing-06);

    &-body {
      &-empty {
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: center;
        height: 120px;
      }
    }

    &-filters {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: space-between;
      gap: var(--gl-spacing-06);
      width: 100%;

      &__buttons {
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: var(--gl-spacing-06);
      }
    }
  }
}
</style>

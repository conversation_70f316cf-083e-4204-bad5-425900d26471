<script lang="ts" setup>
import type { Tab } from '@commons/models'
import type { StatusCardData } from '@code-pricing/models/Status.model'
import type { SnackbarComponentProps } from '@commons/types'
import type {
  WIconComponentProps,
  WSkeletonBaseComponentProps,
  WSkeletonComponentProps
} from '@alice-health/wonderland/dist/types/models'

import { computed, inject, ref, watch } from 'vue'

import { useBrowserDownload } from '@commons/hooks'
import { useMutation, useQuery, useQueryClient } from '@tanstack/vue-query'

import { getPendingPrices, getProcessingStatus } from '@code-pricing/api/queries/prices'
import { downloadPricesCSV, uploadPricesCSV } from '@code-pricing/api/mutations/csv'

import { RouterView } from 'vue-router'
import { ViewLayout } from '@commons/layout'
import { WButton, WModal, WSelect, WSkeleton } from '@alice-health/wonderland-vue'

import Tabs from '@commons/components/Tabs.vue'
import UploadFile from '@commons/components/UploadFile.vue'
import StatusCard from '@code-pricing/components/StatusCard.vue'
import { getEffectiveDates } from '@code-pricing/api/queries/csv'

import { useRouter } from 'vue-router'

/*
 * Constants
 */

const tabs: Tab[] = [
  {
    id: 'list-pricings',
    label: 'Consulta e download',
    route: { name: 'list-pricings' }
  },
  {
    id: 'history',
    label: 'Histórico de cadastro',
    route: { name: 'history' }
  }
]

/*
 * Injections
 */

const snackbar = inject<SnackbarComponentProps>('snackbar')

/*
 * Constants
 */

const statusSkeletonItem: WSkeletonBaseComponentProps = {
  width: 588,
  height: 258,
  borderRadius: 16
}

const statusSkeleton: WSkeletonComponentProps = {
  gap: { row: 20, column: 20 },
  layout: [[statusSkeletonItem, statusSkeletonItem]]
}

/*
 * Hooks
 */

const queryClient = useQueryClient()

const { downloadWithBlob } = useBrowserDownload()

const router = useRouter()

/*
 * Computeds
 */

const todayDateWithHour = computed(() => {
  const today = new Date().toLocaleString('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })

  return today.replace(/\//g, '-').replace(',', '--')
})

/*
 * Requests & Mutations
 */

const { data: effectiveDates } = useQuery({
  queryKey: ['effectiveDates'],
  queryFn: () => getEffectiveDates(),
  select: (data) => data.dates
})

const {
  data: pendingPrices,
  refetch: refetchPendingPrices,
  isLoading: isLoadingPendingPrices
} = useQuery({
  queryKey: ['pendingPrices'],
  queryFn: () => getPendingPrices()
})

const {
  data: processingStatus,
  refetch: refetchProcessingStatus,
  isLoading: isLoadingProcessingStatus
} = useQuery({
  queryKey: ['processingStatus'],
  queryFn: () => getProcessingStatus()
})

const { mutate: handleDownloadCSV } = useMutation({
  mutationFn: () => downloadPricesCSV(pendingPrices.value),
  onSuccess: (data) =>
    downloadWithBlob(data, `prices-pending-${todayDateWithHour.value}`, 'text/csv'),
  onError: () => {
    console.error('Error downloading CSV')
  }
})
const { mutate: uploadPrices, status: uploadStatus } = useMutation({
  mutationFn: (data: { fileContent: File; effectiveDate: string }) =>
    uploadPricesCSV({ ...data, onProgress: handleUploadProgress }),
  onSuccess: () => {
    fileToUpload.value = null
    effectiveDate.value = undefined
    uploadFileKey.value++
    effectiveDateKey.value++

    queryClient.invalidateQueries({ queryKey: ['prices'] })

    notify(
      'Estamos processando sua solicitação. Você será notificado assim que o processo for concluído.',
      'icTaskCheck'
    )
    refetchProcessingStatus()
    setTimeout(() => {
      queryClient.invalidateQueries({ queryKey: ['history'] })
      if (router.currentRoute.value.name !== 'history') {
        router.push({ name: 'history' })
      }
    }, 1000)
  },
  onError: () => {
    notify(
      'Não conseguimos processar sua solicitação. Verifique o arquivo enviado e tente novamente.',
      'icClose'
    )
  }
})

/*
 * Refs & Reactives
 */

const uploadProgress = ref(0)
const showUploadModal = ref(false)

const fileToUpload = ref<File | null>(null)
const effectiveDate = ref<string | undefined>(undefined)

const uploadFileKey = ref(0)
const effectiveDateKey = ref(0)

/*
 * Computeds
 */

const invalidUploadSend = computed(() => {
  return !fileToUpload.value || !effectiveDate.value
})

const loadingCards = computed(() => isLoadingPendingPrices.value || isLoadingProcessingStatus.value)

const isUploading = computed(() => uploadStatus.value === 'pending')
const isProcessing = computed(() => processingStatus.value?.isProcessing)

const showPendingStatusCard = computed(() => !loadingCards.value && pendingPrices.value?.length)
const showDoneStatusCard = computed(() => !loadingCards.value && !showPendingStatusCard.value)
const showUploadingStatusCard = computed(
  () => !loadingCards.value && (isUploading.value || isProcessing.value)
)
const showSendAFileStatusCard = computed(
  () => !loadingCards.value && !showUploadingStatusCard.value
)

const doneStatusCard: StatusCardData = {
  title: 'Tudo certo por aqui!',
  status: 'success',
  description:
    'Não há pendências de precificação, mas se quiser revisar ou atualizar preços, você pode gerar um CSV com códigos específicos abaixo.'
}

const sendAFileStatusCard: StatusCardData = {
  title: 'Cadastro de preços',
  status: 'upload',
  description: 'Cadastre ou atualize preços enviando um arquivo CSV com os dados.'
}

const uploadingStatusCard: StatusCardData = {
  title: 'Cadastro em andamento!',
  status: 'uploading',
  description: 'Aguarde a conclusão para enviar um novo CSV de preços.'
}

const pendingPricesStatusCard = computed<StatusCardData>(() => ({
  status: 'error',
  title: `${pendingPrices?.value?.length} códigos possuem pendências de precificação`,
  description: ' Baixe o CSV, preencha os valores pendentes e envie o arquivo.'
}))

const uploadModalConfirmLabel = computed(() => {
  return isUploading.value ? `Enviando (${uploadProgress.value}%)` : 'Cadastrar preços'
})

/*
 * Methods
 */

function handleUploadProgress(percent: number) {
  uploadProgress.value = percent
}

function notify(message: string, icon: WIconComponentProps['icon']) {
  snackbar?.value.$el.add({ message, icon })
}

function openUploadModal() {
  showUploadModal.value = !showUploadModal.value
}

function closeUploadModal() {
  showUploadModal.value = false
}

function handleUpload() {
  if (!fileToUpload.value) {
    notify('Nenhum arquivo selecionado', 'icClose')
    return
  }

  if (!effectiveDate.value) {
    notify('Nenhuma data de vigência selecionada, selecione-a e tente novamente', 'icClose')
    return
  }

  showUploadModal.value = false

  uploadPrices({
    effectiveDate: effectiveDate.value,
    fileContent: fileToUpload.value
  })
}

function handleFileChange(files: File[] | null) {
  fileToUpload.value = files?.[0] ?? null
}

function handleChangeTab(tab: Tab) {
  refetchPendingPrices()
  refetchProcessingStatus()
}

/*
 * Watchers
 */

watch(uploadProgress, (value) => {
  if (value >= 100) {
    showUploadModal.value = false
  }
})
</script>

<template>
  <ViewLayout title="Precificação de códigos" maxWidth="1280px">
    <template #content>
      <div class="home">
        <Tabs
          :tabs="tabs"
          data-testid="tabs"
          initialActive="list-pricings"
          @onChangeTab="handleChangeTab"
        />

        <WSkeleton
          v-if="isLoadingPendingPrices"
          :layout="statusSkeleton.layout"
          :gap="statusSkeleton.gap"
          data-testid="status-skeleton"
        />

        <div class="home__info-panel">
          <StatusCard v-if="showPendingStatusCard" :data="pendingPricesStatusCard">
            <WButton size="large" @click="handleDownloadCSV">Baixar CSV de preços</WButton>
          </StatusCard>

          <StatusCard v-if="showDoneStatusCard" :data="doneStatusCard" />

          <StatusCard v-if="showUploadingStatusCard" :data="uploadingStatusCard" />

          <StatusCard v-if="showSendAFileStatusCard" :data="sendAFileStatusCard">
            <WButton size="large" @click="openUploadModal">Enviar CSV de preços</WButton>
          </StatusCard>
        </div>

        <RouterView />
      </div>
    </template>
  </ViewLayout>

  <WModal
    id="upload-modal"
    title="Enviar CSV de preços"
    :opened="showUploadModal"
    @WClosed="closeUploadModal"
  >
    <div class="home__upload-modal">
      <UploadFile
        listFiles
        accept=".csv"
        :disabled="isUploading"
        @onFileChange="handleFileChange"
        :key="uploadFileKey"
      />

      <div v-if="effectiveDates" class="home__upload-modal__form">
        <WSelect
          :key="effectiveDateKey"
          v-model="effectiveDate"
          label="Início da vigência"
          placeholder="Selecione a data de início da vigência"
        >
          <option selected disabled>Selecione a data de início da vigência</option>

          <option v-for="date in effectiveDates" :key="date" :value="date">
            {{ date }}
          </option>
        </WSelect>
      </div>

      <div class="home__upload-modal__actions">
        <WButton :loading="isUploading" :disabled="invalidUploadSend" @click="handleUpload">
          {{ uploadModalConfirmLabel }}
        </WButton>
      </div>
    </div>
  </WModal>
</template>

<style lang="scss" scoped>
.home {
  display: flex;
  flex-direction: column;
  gap: var(--gl-spacing-10);
  height: 100%;
  width: 100%;

  &__info {
    &-panel {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(min(100%, 430px), 1fr));
      gap: var(--gl-spacing-06);
      width: 100%;
    }
  }

  &__upload-modal {
    display: flex;
    flex-direction: column;
    gap: var(--gl-spacing-06);
    width: 624px;

    &__form {
      display: flex;
      flex-direction: column;
      gap: var(--gl-spacing-06);
      max-width: 320px;
    }

    &__actions {
      display: flex;
      flex-direction: row;
      justify-content: flex-end;
      align-items: center;
      align-content: center;
      gap: var(--gl-spacing-04);
    }
  }
}
</style>

<script lang="ts" setup>
type Props = {
  hover?: boolean
  fullWidth?: boolean
}

defineProps<Props>()
</script>

<template>
  <div :class="{ card: true, 'full-width': fullWidth, hover }">
    <slot />
  </div>
</template>

<style lang="scss" scoped>
.card {
  display: flex;
  flex-direction: column;

  width: 100%;
  padding: var(--gl-spacing-06);

  border-radius: 16px;
  border: 1px solid var(--gl-color-shades-gray-20);
  background: var(--sys-color-surface-background-light);
}

.full-width {
  width: 100%;
}

.hover {
  &:hover {
    box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.08);
  }
}
</style>

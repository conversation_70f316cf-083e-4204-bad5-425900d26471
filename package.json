{"name": "front-app-backoffice", "version": "1.0.0", "type": "module", "license": "MIT", "scripts": {"serve": "nx run front-app-backoffice:serve", "serve:stg": "nx run front-app-backoffice:serve:staging", "serve:dev1": "nx run front-app-backoffice:serve:dev1", "serve:dev2": " nx run front-app-backoffice:serve:dev2", "serve:dev2:mock": "VITE_ENABLE_MOCK=true nx run front-app-backoffice:serve:dev2", "build": "nx run front-app-backoffice:build", "test": "nx test", "test:skip": "nx test --skip-nx-cache", "test:ui": "vitest --ui --coverage --coverage.reporter='html'", "test:unit:coverage": "nx test --coverage --parallel=1", "lint": "nx lint", "prepare": "husky install", "build-storybook": "echo storybook is WIP", "generate": "clingon"}, "private": true, "dependencies": {"@alice-health/observability": "^3.1.0", "@alice-health/vue-hooks": "0.1.0", "@alice-health/wonderland-vue": "2.33.0", "@tanstack/vue-query": "^5.40.0", "axios": "^1.7.2", "firebase": "^10.11.0", "i18next": "^23.11.5", "pinia": "^3.0.2", "uuid": "^11.1.0", "vue": "3.3.4", "vue-router": "^4.3.1", "zod": "^3.23.8", "zod-i18n-map": "^2.27.0"}, "devDependencies": {"@alice-health/eslint-plugin": "^2.5.0", "@alice-health/unit-presets": "2.1.1", "@nx/cypress": "18.1.2", "@nx/eslint": "18.1.2", "@nx/eslint-plugin": "18.1.2", "@nx/js": "18.1.2", "@nx/vite": "18.1.2", "@nx/vue": "18.1.2", "@nx/web": "18.1.2", "@nx/workspace": "18.1.2", "@pinia/testing": "^1.0.1", "@swc-node/register": "~1.8.0", "@swc/core": "~1.3.85", "@swc/helpers": "~0.5.2", "@tanstack/vue-query-devtools": "^5.56.2", "@testing-library/jest-dom": "^6.4.2", "@testing-library/user-event": "^14.5.2", "@testing-library/vue": "^8.0.3", "@types/node": "18.16.9", "@typescript-eslint/eslint-plugin": "^6.13.2", "@typescript-eslint/parser": "^6.13.2", "@vitejs/plugin-vue": "^4.5.0", "@vitest/coverage-v8": "^3.0.8", "@vitest/ui": "^3.0.8", "@vue/eslint-config-prettier": "7.1.0", "@vue/eslint-config-typescript": "^11.0.3", "@vue/test-utils": "^2.4.1", "clingon": "^1.2.0", "cypress": "^13.6.6", "dotenv": "^16.4.5", "eslint": "~8.57.1", "eslint-config-prettier": "^9.0.0", "eslint-plugin-cypress": "^2.13.4", "eslint-plugin-jest-dom": "^5.1.0", "eslint-plugin-testing-library": "^6.2.0", "eslint-plugin-vue": "^9.16.1", "git-commit-msg-linter": "^5.0.7", "husky": "8.0.3", "jsdom": "~22.1.0", "lint-staged": "^15.2.2", "msw": "^2.3.1", "nx": "18.1.2", "prettier": "^2.6.2", "sass": "1.62.1", "typescript": "~5.3.2", "vite": "~6.2.1", "vite-plugin-dts": "~2.3.0", "vitest": "^3.0.8", "vue-tsc": "^1.8.8"}, "nx": {"includedScripts": []}, "msw": {"workerDirectory": [""]}}